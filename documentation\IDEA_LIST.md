# Idea List from Players / Staff In-Game

## User Interface Improvements

### Keyring Wearable Item
- **Description**: Make keyrings a wearable item (belt slot) that allows keys to be used while stored
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 13), <PERSON><PERSON> (Level 12)
- **Status**: Belt slot exists, key system exists, but no keyring container - excellent QoL improvement

### In-Character Communication
- **Description**: Telepathy item system in unique slot, restringable and craftable for IC communication
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 13)
- **Status**: Would enhance roleplay, unique slot system exists

### Charmee Affects Display
- **Description**: Command to check what spells/buffs are active on charmees
- **Submitted by**: Plixid (Level 8)
- **Status**: Charmee system exists but lacks affect visibility - good utility feature

### In Wilderness Only: Location Coordinates on Prompt
- **Description**: Add room coordinates to prompt display
- **Submitted by**: Age (Level 10)
- **Status**: Coordinate system exists but not in prompt - useful for navigation

### Help in Character Creation
- **Description**: Enable help files during character creation process
- **Submitted by**: <PERSON><PERSON><PERSON> (Level 34)
- **Status**: Help system exists but not in chargen - would help new players

### Defensive Stance Notification
- **Description**: Alert when stalwart defender's defensive stance expires
- **Submitted by**: Brondo (Level 30)
- **Status**: Class-specific QoL improvement for defensive stance users

### Compare Command
- **Description**: Add command to compare weapons and armor stats
- **Submitted by**: Darowin (Level 3)
- **Status**: Equipment system exists - very useful utility command

## Crafting System

### Stop/Cancel Crafting Command
- **Description**: Command to interrupt crafting process
- **Submitted by**: Dagmar (Level 1)
- **Status**: Crafting system exists but lacks interruption - important QoL

### Early Area Crafting Molds
- **Description**: Add armor and cloth molds to Ashenport crafting store
- **Submitted by**: Mendev (Level 20)
- **Status**: Content addition for early game crafting accessibility

### Restring Enhancements
- **Description**: Allow modification of description and material in addition to name
- **Submitted by**: Metvagen (Level 19)
- **Status**: Restring exists but limited - would enhance customization

### Periodic Crafting Bonuses
- **Description**: Crafters gain XP when their crafted items are used in combat
- **Submitted by**: Gicker (Level 34), originally by Tollymore
- **Status**: Interesting crafting incentive system

### Crafting Missions from Bazaar
- **Description**: Create crafting missions when players order from Bazaar, paying quest points
- **Submitted by**: Tollymore (Level 8)
- **Status**: Mission system exists - would integrate crafting with economy

### Crafting Recipe Adventure Loop
- **Description**: Recipe system with key components from spawned crafting nodes with guardians
- **Submitted by**: Tollymore (Level 8)
- **Status**: Would add adventure element to crafting

### Weapon Type Conversion
- **Description**: Crafting method to change weapon form without losing enchantments
- **Submitted by**: Murdoch (Level 13)
- **Status**: Useful for weapon customization without losing progress

## Combat & Class Features

### Class Feats Display for Wizards
- **Description**: Show wizard bonus feats every 5 levels in class feats display
- **Submitted by**: Ortallus (Level 4)
- **Status**: Class system exists - good information display improvement

### Berserker Acrobatics
- **Description**: Add acrobatics as class skill for berserkers (Pathfinder alignment)
- **Submitted by**: Mendev (Level 23)
- **Status**: Class skill system exists - reasonable class enhancement

### Naming Companions
- **Description**: Optional command to assign names to followers/summons/companions
- **Submitted by**: Valafar (Level 9)
- **Status**: Companion system exists - good roleplay enhancement

### Greater Feint Feat
- **Description**: New feat allowing feint as move action
- **Submitted by**: Yure (Level 3)
- **Status**: Feat system exists - reasonable combat feat addition

### Zombie Bite Attacks
- **Description**: Add bite and occasional blunt attacks to animated dead zombies
- **Submitted by**: Metvagen (Level 12)
- **Status**: Undead system exists - would make zombies more interesting

### Rogue Poison Creation
- **Description**: Weak, long-lasting poison creation for rogues without expensive materials
- **Submitted by**: Serul (Level 26)
- **Status**: Poison system exists - good class-specific utility

### Summon Shadow Teamwork
- **Description**: Grant shadow any teamwork feats the caster has
- **Submitted by**: Raiko (Level 15)
- **Status**: Teamwork feat system exists - logical enhancement

### Non-Druid Wildshape Forms
- **Description**: Allow viewing wildshape forms for polymorph self spell
- **Submitted by**: Ilzude (Level 23)
- **Status**: Wildshape system exists - good information display

### Healing Wave Spell
- **Description**: Multi-round healing spell (100-200 HP per round for 3-5 rounds)
- **Submitted by**: Melow (Level 30)
- **Status**: Spell system exists - interesting healing mechanic

### Damage Display Toggle
- **Description**: Toggle to view damage from other players/pets
- **Submitted by**: Brondo (Level 30)
- **Status**: Combat system exists - useful information toggle

### Careful With Pets Default
- **Description**: Make "careful with pets" on by default
- **Submitted by**: Malicor (Level 23)
- **Status**: Pet system exists - good default setting change

### Circle/Backstab on Blind/Paralyzed
- **Description**: Allow circle when enemy is blind/paralyzed even if tanking
- **Submitted by**: Murdoch (Level 30)
- **Status**: Combat system exists - reasonable tactical enhancement

### Class-Specific AI Casting
- **Description**: Mob casting tables by class for more realistic spellcasting
- **Submitted by**: Dudris (Level 31)
- **Status**: AI system exists - would improve mob intelligence

### Circle Combat Initiation
- **Description**: Allow circle command to engage combat when someone else is tanking
- **Submitted by**: Badase (Level 30)
- **Status**: Combat system exists - tactical improvement

### Buffself Stand Command
- **Description**: Auto-stand when using "buffself perform" command
- **Submitted by**: Lyllian (Level 7)
- **Status**: Performance system exists - good QoL improvement

## Game Mechanics

### Food/Water Rest Bonus
- **Description**: HP recovery bonus while resting with food/water
- **Submitted by**: Mendev (Level 17)
- **Status**: Rest system exists - logical survival mechanic

### Temple Association
- **Description**: Temple affiliation system filling God slot, providing quest chains
- **Submitted by**: Arvaunshae (Level 17)
- **Status**: Religion system exists - would enhance roleplay

### PSP on GUI
- **Description**: Add psionic power points to GUI display
- **Submitted by**: Zusuk (Level 34)
- **Status**: Psionics exist, GUI exists - good information display

### Auto-Sacrifice Toggle
- **Description**: Auto-grab items when sacrificing corpses
- **Submitted by**: Metvagen (Level 20)
- **Status**: Sacrifice system exists - useful automation

### Message Speed Control
- **Description**: Slow down NPC message delivery for readability
- **Submitted by**: Delax (Level 1)
- **Status**: Communication system exists - accessibility improvement

### Screen Reader Support
- **Description**: Better integration with screen reader detection
- **Submitted by**: Harlon (Level 3)
- **Status**: Accessibility improvement - important for disabled players

### Unlockable Feats List
- **Description**: Show locked feats and their requirements
- **Submitted by**: Dasvel (Level 28)
- **Status**: Feat system exists - good information display

### Independent Feat Changes
- **Description**: Allow feat changes without full respec
- **Submitted by**: Yaran (Level 9)
- **Status**: Feat system exists - good QoL improvement

### Read During Crafting
- **Description**: Allow reading news/motd while crafting
- **Submitted by**: Kormundrad (Level 3)
- **Status**: Both systems exist - good multitasking feature

### Store Food/Drink
- **Description**: Add food/drink enhancements to store feature
- **Submitted by**: Brondo (Level 30)
- **Status**: Store system exists - logical enhancement

### Poison Vial Cleanup
- **Description**: Empty poison vials should disappear after last use
- **Submitted by**: Badase (Level 30)
- **Status**: Poison system exists - good cleanup mechanic

### Locate Object Filter
- **Description**: Exclude items in player houses from locate object
- **Submitted by**: Diel (Level 30)
- **Status**: Locate system exists - prevents house snooping

### Mission Location Mapping
- **Description**: Only create missions to mapped locations
- **Submitted by**: Tanaka (Level 13)
- **Status**: Mission system exists - prevents impossible missions

### Save Command Hint
- **Description**: Add save command instruction to tutorial/hints
- **Submitted by**: Fish (Level 1)
- **Status**: Tutorial system exists - important for new players

### Quest Item Drop Command
- **Description**: Command for quest mobs to drop inventory items without killing
- **Submitted by**: Arithon (Level 30)
- **Status**: Quest system exists - useful for quest design

### Feat Point Documentation
- **Description**: Explain different types of feat points and their uses
- **Submitted by**: Neurrone (Level 21)
- **Status**: Feat system exists - important documentation improvement

### Polymorph Self Clarification
- **Description**: Help file should mention no race feats when polymorphed
- **Submitted by**: Neurrone (Level 22)
- **Status**: Polymorph system exists - documentation improvement

### Shifter Dragon Buffs
- **Description**: Increase dragon form breath weapon and spell damage significantly
- **Submitted by**: Ogoun (Level 30)
- **Status**: Shifter class exists - balance improvement for underused forms

## Quality of Life

### Unlock Door Message
- **Description**: Change "*click*" to "You unlock the door"
- **Submitted by**: Delax (Level 1)
- **Status**: Door system exists - simple message improvement

### Colorful OK Messages
- **Description**: Replace generic "okay" with more colorful responses
- **Submitted by**: Zusuk (Level 34)
- **Status**: Message system exists - flavor improvement

### Harvesting Depletion Message
- **Description**: Show "depleted" at end of harvest process instead of warning
- **Submitted by**: Variel (Level 2)
- **Status**: Harvesting system exists - better user feedback

### Beginning Journey for Blind
- **Description**: Add coordinates of half-orc camp to quest description
- **Submitted by**: Ozrim (Level 3)
- **Status**: Quest system exists - accessibility improvement

### Psionic Power Conservation
- **Description**: Don't consume PSP when power can't be manifested
- **Submitted by**: Fyre (Level 3)
- **Status**: Psionics exist - prevents resource waste

### Intimidate Duration Info
- **Description**: Add duration information to intimidate help file
- **Submitted by**: Mendev (Level 10)
- **Status**: Intimidate exists - documentation improvement

### Staff Helper for Queues
- **Description**: Add field to categorize bug/typo/idea submissions by type
- **Submitted by**: Jordan (Level 31)
- **Status**: Bug system exists - staff workflow improvement

### Spell Class/Circle Info
- **Description**: Add class and circle information to spell help files
- **Submitted by**: Murdoch (Level 30)
- **Status**: Spell system exists - important information display

### Gear Splitting System
- **Description**: Dice roll or bidding system for fair loot distribution
- **Submitted by**: Lamix (Level 30)
- **Status**: Group system exists - would reduce loot disputes

### Mosswood Elder Directions
- **Description**: Have "ask elder ashenport" give directions to Ashenport
- **Submitted by**: Mddljeu (Level 2)
- **Status**: NPC system exists - specific content improvement

## New Spells & Abilities

### Dimension Door
- **Description**: Implement dimension door spell
- **Submitted by**: Gargar (Level 22)
- **Status**: Spell system exists - classic D&D spell addition

### Dispel Invisibility
- **Description**: Spell to remove invisibility from equipment
- **Submitted by**: Melaw (Level 30)
- **Status**: Invisibility system exists - useful counter-spell

### Area Wall Spells
- **Description**: Prismatic cube or force cube area denial spells
- **Submitted by**: Melaw (Level 30)
- **Status**: Spell system exists - tactical area control spells

### Healer Class Unlock
- **Description**: Prestige class with healing bonuses and faster memorization
- **Submitted by**: Melaw (Level 30)
- **Status**: Class system exists - specialized healing class

## NPC & World Interaction

### Ashenport Tourist Quests
- **Description**: Small escort missions for lost tourists in city
- **Submitted by**: Metvagen (Level 12)
- **Status**: Quest system exists - good low-level content

### Smoking System
- **Description**: Immersive smoking system for roleplay
- **Submitted by**: Arvaunshae (Level 17)
- **Status**: Item system exists - roleplay enhancement

## Accessibility

### Symbol Alternatives
- **Description**: Use letters/words instead of punctuation for screen readers
- **Submitted by**: Dranulous (Level 2)
- **Status**: Display system exists - important accessibility improvement

### Plane Travel Effect
- **Description**: Add humorous message for maintained velocity after recall from falling
- **Submitted by**: Iliri (Level 26)
- **Status**: Recall system exists - fun flavor addition

