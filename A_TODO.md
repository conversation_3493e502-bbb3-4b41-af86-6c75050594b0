# ToDo List - External Resource Management Audit

Generated: 2025-07-28

## Summary

Audit of external resource management (excluding MySQL) found the codebase generally handles resources well. Most file operations have proper cleanup in both success and error paths. No major resource leaks were identified beyond those already fixed in recent changelogs.

## Audit Results

### File Operations Audit ✓
- **Status**: GOOD - All major file operations properly close handles
- **Files Checked**: ai_security.c, act.wizard.c, ban.c, mail.c, comm.c, boards.c, clan_edit.c
- **Pattern**: Consistent use of fclose() after fopen()
- **Error Handling**: Proper cleanup in error paths (e.g., mail.c closes first file if second fails)

### Socket Operations Audit ✓
- **Status**: GOOD - Socket management in comm.c is properly handled
- **Main Socket**: Created once, reused throughout server lifetime
- **Client Sockets**: Properly closed on disconnect via close_socket()
- **No Issues Found**: Socket descriptor management appears sound

### CURL Resource Management ✓
- **Status**: GOOD - Proper initialization and cleanup
- **Global Init**: curl_global_init() with proper error checking
- **Persistent Handle**: Single handle reused for performance
- **Cleanup**: Proper curl_easy_cleanup() and curl_global_cleanup()
- **Error Paths**: All error paths clean up resources correctly

### Directory Operations ✓
- **Status**: GOOD - Limited usage, properly handled
- **Files**: util/rebuildMailIndex.c, util/rebuildAsciiIndex.c
- **Pattern**: opendir() always paired with closedir()

### Log File Management ✓
- **Status**: GOOD - Single global logfile handle
- **Pattern**: Opened once at startup, used throughout
- **No Overwrites**: Uses freopen() when changing log files

## Recent Fixes Already Addressed

The CHANGELOG shows extensive resource leak fixes already completed:

1. **File Handle Leaks** (Fixed)
   - act.wizard.c: find_llog_entry() - missing fclose on error path
   - act.wizard.c: list_llog_entries() - missing fclose and return

2. **Memory Leaks** (Fixed)
   - Multiple string reassignment leaks (free before strdup)
   - Event system variable leaks
   - DG Scripts lookup table leaks
   - Character description function leaks
   - Crafting system node creation leaks
   - AI service shutdown cleanup

3. **MySQL Result Leaks** (Fixed)
   - load_account() - missing mysql_free_result()

## Minor Issues Found

### 1. zmalloc.c Global File Handle
- **Location**: zmalloc.c:75
- **Issue**: `zfd = fopen("zmalloc.log", "w+");` - No check if already open
- **Risk**: LOW - Debug code, not used in production
- **Recommendation**: Add check for existing handle before opening

### 2. Potential Logfile Overwrite
- **Location**: comm.c:3768
- **Issue**: Direct assignment to logfile without closing previous
- **Risk**: LOW - Only happens during startup/command line parsing
- **Note**: Uses freopen() in other cases which is correct

## Recommendations

### High Priority
1. **No high priority issues found** - The codebase handles resources well

### Medium Priority
1. **Add Resource Tracking**: Consider adding debug mode resource tracking
   - Track all fopen/fclose pairs
   - Log unclosed handles at shutdown
   - Useful for catching future leaks

2. **Standardize Error Messages**: Some fopen failures use perror(), others use log()
   - Recommend standardizing on log() for consistency

### Low Priority
1. **Code Review**: zmalloc.c debug logging
   - Either remove if unused or add proper handle management

2. **Documentation**: Add resource management guidelines to CLAUDE.md
   - Document the patterns for proper resource cleanup
   - Include examples of error path handling

## Best Practices Observed

1. **Consistent Patterns**:
   ```c
   if (!(fp = fopen(filename, "r"))) {
     log("Error opening file");
     return ERROR;
   }
   // ... use file ...
   fclose(fp);
   ```

2. **Error Path Cleanup**:
   ```c
   if (!(fp1 = fopen(file1, "r")))
     return ERROR;
   if (!(fp2 = fopen(file2, "r"))) {
     fclose(fp1);  // Clean up first file
     return ERROR;
   }
   ```

3. **CURL Pattern**:
   ```c
   curl = curl_easy_init();
   if (!curl) return ERROR;
   // ... use curl ...
   if (curl != persistent_handle)
     curl_easy_cleanup(curl);
   ```

## Conclusion

The LuminariMUD codebase demonstrates good resource management practices. The recent fixes in the CHANGELOG show active attention to resource leaks. No critical issues were found during this audit. The codebase properly:

- Closes all opened files
- Manages socket descriptors correctly
- Cleans up CURL resources
- Handles error paths with proper cleanup
- Uses consistent patterns throughout

The main recommendation is to maintain these good practices and consider adding debug-mode resource tracking for future leak detection.

